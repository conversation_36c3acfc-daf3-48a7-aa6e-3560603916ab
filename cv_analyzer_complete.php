<?php
/**
 * Complete CV Analyzer Script - PHP Version
 * Analyzes CV PDFs using Google Gemini Flash model and outputs structured JSON.
 * 
 * Usage:
 *     php cv_analyzer_complete.php <pdf_path>
 *     php cv_analyzer_complete.php pdf_cvs/file.pdf
 *     php cv_analyzer_complete.php /full/path/to/cv.pdf
 * 
 * Features:
 * - Loads API key from .env file
 * - Analyzes CV with Gemini Flash model
 * - Extracts structured information
 * - Saves results to JSON file
 * - All-in-one script with no external dependencies (except cURL)
 */

class CVAnalyzer {
    private $apiKey;
    private $apiUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent';
    
    public function __construct($apiKey = null) {
        $this->apiKey = $apiKey ?: $this->loadApiKey();
        
        if (!$this->apiKey) {
            throw new Exception("No API key found. Please set api_key in .env file or pass it as parameter.");
        }
    }
    
    /**
     * Load API key from .env file
     */
    private function loadApiKey() {
        $envFile = '.env';
        
        if (!file_exists($envFile)) {
            return null;
        }
        
        $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        
        foreach ($lines as $line) {
            $line = trim($line);
            if (strpos($line, '#') === 0 || strpos($line, '=') === false) {
                continue;
            }
            
            list($key, $value) = explode('=', $line, 2);
            $key = trim($key);
            $value = trim($value, " \t\n\r\0\x0B\"'");
            
            if (in_array($key, ['api_key', 'GOOGLE_AI_API_KEY', 'GEMINI_API_KEY', 'API_KEY'])) {
                return $value;
            }
        }
        
        return null;
    }
    
    /**
     * Analyze CV PDF using Gemini Flash model
     */
    public function analyzeCv($pdfPath) {
        if (!file_exists($pdfPath)) {
            throw new Exception("CV file not found: $pdfPath");
        }
        
        // Read and encode PDF
        $pdfContent = base64_encode(file_get_contents($pdfPath));
        
        // Detailed prompt for CV analysis
        $prompt = '
        Please analyze this CV/Resume document and extract the information in the following EXACT JSON format:

        {
            "fullname": "Full name of the candidate",
            "email": "Email of the candidate",
            "phone": "Phone number of the candidate",
            "title": "CV title or professional headline",
            "summary": "Professional summary or objective (brief description of the candidate)",
            "location": "City, Country (candidate\'s location)",
            "website": "LinkedIn profile URL or personal website",
            "skills": [
                {"name": "Skill name", "level": "Expert|Avancé|Intermédiaire|Débutant"},
                {"name": "Another skill", "level": "Expert|Avancé|Intermédiaire|Débutant"}
            ],
            "experiences": [
                {
                    "title": "Job title",
                    "company": "Company name",
                    "location": "City, Country",
                    "start_date": "YYYY-MM-DD",
                    "end_date": "YYYY-MM-DD or null if current",
                    "is_current": true/false,
                    "description": "Job description and responsibilities"
                }
            ],
            "educations": [
                {
                    "degree": "Degree name",
                    "institution": "Institution name",
                    "location": "City, Country",
                    "start_date": "YYYY-MM-DD",
                    "end_date": "YYYY-MM-DD",
                    "description": "Education description or specialization"
                }
            ],
            "languages": [
                {"name": "Language name", "level": "Natif|Courant|Intermédiaire|Débutant"}
            ]
        }

        IMPORTANT INSTRUCTIONS:
        - Use EXACT field names as shown above
        - Extract the full name, email, and phone of the candidate
        - For skill levels, use: "Expert", "Avancé", "Intermédiaire", or "Débutant"
        - For language levels, use: "Natif", "Courant", "Intermédiaire", or "Débutant"
        - For dates, use YYYY-MM-DD format (estimate if exact date not available)
        - Set "is_current" to true for current positions, false for past positions
        - Set "end_date" to null for current positions
        - If title is not explicitly mentioned, create a professional title based on the CV content
        - If website is not available, use LinkedIn profile or set to null
        - Include ALL skills found in the CV with appropriate skill levels
        - Include ALL work experiences with complete information
        - Include ALL education entries
        - Include ALL languages mentioned

        Please respond ONLY with the JSON object, no additional text.
        ';
        
        // Prepare request data
        $data = [
            'contents' => [
                [
                    'parts' => [
                        ['text' => $prompt],
                        [
                            'inline_data' => [
                                'mime_type' => 'application/pdf',
                                'data' => $pdfContent
                            ]
                        ]
                    ]
                ]
            ]
        ];
        
        // Make API request
        $url = $this->apiUrl . '?key=' . $this->apiKey;
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json'
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 120);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            throw new Exception("cURL error: $error");
        }
        
        if ($httpCode !== 200) {
            throw new Exception("API request failed with HTTP code: $httpCode. Response: $response");
        }
        
        $responseData = json_decode($response, true);
        
        if (!$responseData || !isset($responseData['candidates'][0]['content']['parts'][0]['text'])) {
            throw new Exception("Invalid API response format");
        }
        
        $responseText = $responseData['candidates'][0]['content']['parts'][0]['text'];
        
        // Extract JSON from response
        return $this->extractJsonFromResponse($responseText);
    }
    
    /**
     * Extract JSON data from Gemini response text
     */
    private function extractJsonFromResponse($responseText) {
        // Try to find JSON content between ```json and ``` markers
        if (preg_match('/```json\s*(.*?)\s*```/s', $responseText, $matches)) {
            $jsonStr = $matches[1];
        } elseif (preg_match('/\{.*\}/s', $responseText, $matches)) {
            // Try to find JSON content between { and } markers
            $jsonStr = $matches[0];
        } else {
            // If no JSON markers found, assume the entire response is JSON
            $jsonStr = $responseText;
        }
        
        $cvData = json_decode($jsonStr, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            return [
                'error' => 'Failed to parse JSON response',
                'raw_response' => $responseText,
                'parse_error' => json_last_error_msg()
            ];
        }
        
        return $cvData;
    }
    
    /**
     * Save CV analysis results to JSON file
     */
    public function saveCvAnalysis($cvData, $outputFile) {
        // Create output directory if it doesn't exist
        $outputDir = dirname($outputFile);
        if ($outputDir && !is_dir($outputDir)) {
            mkdir($outputDir, 0755, true);
            echo "📁 Created directory: $outputDir\n";
        }
        
        $jsonData = json_encode($cvData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        
        if (file_put_contents($outputFile, $jsonData) === false) {
            throw new Exception("Failed to save CV analysis to: $outputFile");
        }
        
        echo "💾 CV analysis saved to $outputFile\n";
    }
    
    /**
     * Print CV analysis summary
     */
    public function printCvSummary($cvData) {
        if (isset($cvData['error'])) {
            echo "❌ Error: {$cvData['error']}\n";
            return;
        }
        
        echo "\n📊 CV Analysis Summary:\n";
        echo str_repeat("=", 50) . "\n";
        
        // Full Name, Email, Phone
        if (isset($cvData['fullname'])) {
            echo "👤 Full Name: {$cvData['fullname']}\n";
        }
        if (isset($cvData['email'])) {
            echo "📧 Email: {$cvData['email']}\n";
        }
        if (isset($cvData['phone'])) {
            echo "📱 Phone: {$cvData['phone']}\n";
        }
        
        // Title and Summary
        if (isset($cvData['title'])) {
            echo "📋 Title: {$cvData['title']}\n";
        }
        
        if (isset($cvData['summary']) && !empty($cvData['summary'])) {
            $summary = $cvData['summary'];
            echo "💼 Summary:\n";
            echo "   " . (strlen($summary) > 200 ? substr($summary, 0, 200) . '...' : $summary) . "\n";
        }
        
        // Location and Website
        if (isset($cvData['location'])) {
            echo "📍 Location: {$cvData['location']}\n";
        }
        if (isset($cvData['website']) && !empty($cvData['website'])) {
            echo "🌐 Website: {$cvData['website']}\n";
        }
        
        echo "\n";
        
        // Work Experience
        if (isset($cvData['experiences']) && is_array($cvData['experiences'])) {
            $expCount = count($cvData['experiences']);
            echo "🏢 Work Experience ($expCount positions):\n";
            
            foreach (array_slice($cvData['experiences'], 0, 3) as $i => $job) {
                $currentStatus = isset($job['is_current']) && $job['is_current'] ? ' (Current)' : '';
                echo "   " . ($i + 1) . ". {$job['title']} at {$job['company']}$currentStatus\n";
                
                $startDate = $job['start_date'] ?? 'N/A';
                $endDate = (isset($job['is_current']) && $job['is_current']) ? 'Present' : ($job['end_date'] ?? 'N/A');
                echo "      Duration: $startDate to $endDate\n";
            }
            
            if ($expCount > 3) {
                echo "   ... and " . ($expCount - 3) . " more\n";
            }
            echo "\n";
        }
        
        // Education
        if (isset($cvData['educations']) && is_array($cvData['educations'])) {
            $eduCount = count($cvData['educations']);
            echo "🎓 Education ($eduCount entries):\n";
            
            foreach ($cvData['educations'] as $i => $edu) {
                echo "   " . ($i + 1) . ". {$edu['degree']} from {$edu['institution']}\n";
                $startDate = $edu['start_date'] ?? 'N/A';
                $endDate = $edu['end_date'] ?? 'N/A';
                echo "      Period: $startDate to $endDate\n";
            }
            echo "\n";
        }
        
        // Skills
        if (isset($cvData['skills']) && is_array($cvData['skills'])) {
            $skillCount = count($cvData['skills']);
            echo "🔧 Skills ($skillCount skills):\n";
            
            foreach (array_slice($cvData['skills'], 0, 5) as $skill) {
                echo "   • {$skill['name']} ({$skill['level']})\n";
            }
            
            if ($skillCount > 5) {
                echo "   ... and " . ($skillCount - 5) . " more\n";
            }
            echo "\n";
        }
        
        // Languages
        if (isset($cvData['languages']) && is_array($cvData['languages'])) {
            $langCount = count($cvData['languages']);
            echo "🌍 Languages ($langCount languages):\n";
            
            foreach ($cvData['languages'] as $lang) {
                echo "   • {$lang['name']} ({$lang['level']})\n";
            }
            echo "\n";
        }
    }
}

// Main execution
function main() {
    echo "🔍 Complete CV Analyzer - PHP Version\n";
    echo str_repeat("=", 50) . "\n";
    
    // Check command line arguments
    if ($argc < 2) {
        echo "❌ Error: Please provide a PDF file path\n";
        echo "Usage: php cv_analyzer_complete.php <pdf_path>\n";
        echo "Examples:\n";
        echo "  php cv_analyzer_complete.php pdf_cvs/file.pdf\n";
        echo "  php cv_analyzer_complete.php /full/path/to/cv.pdf\n";
        echo "  php cv_analyzer_complete.php C:\\Users\\<USER>\\cv.pdf\n";
        exit(1);
    }
    
    $pdfPath = $argv[1];
    
    // Generate output filename
    $parentDir = dirname(dirname($pdfPath)) ?: '.';
    $jsonDir = $parentDir . DIRECTORY_SEPARATOR . 'json_cvs';
    $baseName = pathinfo($pdfPath, PATHINFO_FILENAME);
    $outputFile = $jsonDir . DIRECTORY_SEPARATOR . $baseName . '.json';
    
    echo "📄 Input PDF: $pdfPath\n";
    echo "💾 Output JSON: $outputFile\n";
    
    // Check if PDF file exists
    if (!file_exists($pdfPath)) {
        echo "❌ Error: PDF file not found: $pdfPath\n";
        exit(1);
    }
    
    try {
        // Initialize analyzer
        echo "🔑 Checking API key...\n";
        $analyzer = new CVAnalyzer();
        echo "✅ API key found\n";
        
        // Analyze the CV
        echo "🤖 Analyzing CV with Gemini Flash model...\n";
        $cvData = $analyzer->analyzeCv($pdfPath);
        
        // Check for errors in response
        if (isset($cvData['error'])) {
            echo "❌ Analysis failed: {$cvData['error']}\n";
            if (isset($cvData['raw_response'])) {
                echo "Raw response: " . substr($cvData['raw_response'], 0, 200) . "...\n";
            }
            exit(1);
        }
        
        // Save results
        $analyzer->saveCvAnalysis($cvData, $outputFile);
        
        // Display summary
        $analyzer->printCvSummary($cvData);
        
        echo "🎉 Analysis completed successfully!\n";
        echo "📂 Full details saved in: $outputFile\n";
        
    } catch (Exception $e) {
        echo "❌ Unexpected error: " . $e->getMessage() . "\n";
        echo "Please check your API key and internet connection\n";
        exit(1);
    }
}

// Run the script
if (php_sapi_name() === 'cli') {
    main();
}
?>
