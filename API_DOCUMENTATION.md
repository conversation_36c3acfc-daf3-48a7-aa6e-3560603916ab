# CV Analysis API Documentation

This API provides asynchronous CV analysis using Google's Gemini Flash model. It consists of two main endpoints for submitting analysis jobs and tracking their status.

## 🚀 Quick Start

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Start the API server:**
   ```bash
   python cv_api.py
   ```

3. **Server will run on:** `http://localhost:5000`

## 📋 API Endpoints

### 1. Submit CV for Analysis

**Endpoint:** `POST /analyze`

**Description:** Submit a CV file for analysis. Returns a job ID for tracking.

**Request Body:**
```json
{
    "filename": "file.pdf"
}
```

**Response (202 Accepted):**
```json
{
    "job_id": "550e8400-e29b-41d4-a716-446655440000",
    "status": "pending",
    "message": "CV analysis job created successfully"
}
```

**Error Response (400/404/500):**
```json
{
    "error": "Error message"
}
```

### 2. Check Analysis Status

**Endpoint:** `GET /status/<job_id>`

**Description:** Check the status of an analysis job and get results when completed.

**Response - Pending/Running (200 OK):**
```json
{
    "job_id": "550e8400-e29b-41d4-a716-446655440000",
    "status": "running",
    "message": "running....",
    "created_at": "2024-01-15T10:30:00",
    "updated_at": "2024-01-15T10:31:00"
}
```

**Response - Completed (200 OK):**
```json
{
    "job_id": "550e8400-e29b-41d4-a716-446655440000",
    "status": "completed",
    "message": "Analysis completed successfully",
    "created_at": "2024-01-15T10:30:00",
    "updated_at": "2024-01-15T10:32:00",
    "output_file": "file_analysis.json",
    "result": {
        "personal_information": {
            "full_name": "John Doe",
            "email_address": "<EMAIL>",
            "phone_number": "+1234567890",
            "address": "City, Country"
        },
        "work_experience": [...],
        "education": [...],
        "skills": {...},
        "certifications": [...],
        "projects": [...],
        "languages": [...]
    }
}
```

**Response - Failed (200 OK):**
```json
{
    "job_id": "550e8400-e29b-41d4-a716-446655440000",
    "status": "failed",
    "message": "Analysis failed: API key error",
    "error": "No API key found",
    "created_at": "2024-01-15T10:30:00",
    "updated_at": "2024-01-15T10:31:00"
}
```

### 3. List All Jobs (Debug)

**Endpoint:** `GET /jobs`

**Description:** List all analysis jobs (for monitoring/debugging).

**Response (200 OK):**
```json
{
    "jobs": [
        {
            "job_id": "550e8400-e29b-41d4-a716-446655440000",
            "filename": "file.pdf",
            "status": "completed",
            "message": "Analysis completed successfully",
            "created_at": "2024-01-15T10:30:00",
            "updated_at": "2024-01-15T10:32:00"
        }
    ],
    "total": 1
}
```

### 4. Health Check

**Endpoint:** `GET /health`

**Description:** Check if the API server is running.

**Response (200 OK):**
```json
{
    "status": "healthy",
    "message": "CV Analysis API is running",
    "timestamp": "2024-01-15T10:30:00"
}
```

## 🔄 Workflow Example

### Using curl:

1. **Submit CV for analysis:**
   ```bash
   curl -X POST http://localhost:5000/analyze \
        -H "Content-Type: application/json" \
        -d '{"filename": "file.pdf"}'
   ```

2. **Check status (repeat until completed):**
   ```bash
   curl http://localhost:5000/status/YOUR_JOB_ID
   ```

### Using Python requests:

```python
import requests
import time

# Submit analysis
response = requests.post('http://localhost:5000/analyze', 
                        json={'filename': 'file.pdf'})
job_id = response.json()['job_id']

# Poll for results
while True:
    status_response = requests.get(f'http://localhost:5000/status/{job_id}')
    data = status_response.json()
    
    if data['status'] == 'completed':
        cv_analysis = data['result']
        break
    elif data['status'] == 'failed':
        print(f"Analysis failed: {data['error']}")
        break
    else:
        print("running....")
        time.sleep(5)  # Wait 5 seconds before checking again
```

## 📁 File Structure

```
cv_ai/
├── cv_api.py              # Main API server
├── test_api.py            # Test client
├── cv_analyzer.py         # CV analysis logic
├── config.py              # Configuration
├── .env                   # API key
├── requirements.txt       # Dependencies
└── pdf_cvs/
    ├── file.pdf          # CV files
    └── file2.pdf
```

## ⚙️ Configuration

Make sure you have:

1. **API Key in .env file:**
   ```
   api_key="your-google-ai-api-key-here"
   ```

2. **CV files in pdf_cvs/ folder**

3. **Dependencies installed:**
   ```bash
   pip install -r requirements.txt
   ```

## 🧪 Testing

Run the test client:
```bash
python test_api.py
```

This will:
1. Check API health
2. Submit a CV for analysis
3. Poll for status until completion
4. Display results

## 🔧 Status Codes

- **202 Accepted:** Job created successfully
- **200 OK:** Status retrieved successfully
- **400 Bad Request:** Invalid request (missing filename)
- **404 Not Found:** Job or file not found
- **500 Internal Server Error:** Server error

## 📝 Notes

- Jobs are stored in memory (use Redis/database for production)
- Analysis runs asynchronously in background threads
- API supports CORS for web applications
- Files must be placed in `pdf_cvs/` folder before analysis
- Output files are saved as `{filename}_analysis.json`
