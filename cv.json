{"personal_information": {"full_name": "AHMAD BEN ABDALLAH", "email_address": "contactbenab<PERSON><PERSON><PERSON><PERSON>@gmail.com", "phone_number": "+216 26567756", "address": "Sfax, Tunisia", "linkedin_profile": "www.linkedin.com/in/ahmad-ben<PERSON><PERSON><PERSON>-", "other_links": ["https://github.com/ahamdbenabdallah", "https://ahamdbenabdalla.github.io/portfolio-website-3d/"]}, "professional_summary": "Étudiant passionné en informatique, actuellement en lère année d'ingénierie en génie informatique à l'IIT.\nFortement intéressé par le développement web et mobile, je suis curieux, autonome et motivé par l'apprentissage continu.", "work_experience": [{"job_title": "Stage d'été", "company_name": "DSP", "duration": "Juillet - Août 2023", "location": null, "responsibilities": ["Développement d'une plateforme de gestion de stock dans le cadre d'un projet de développement web dynamique, au sein de la startup DSP.", "Utilisation des technologies Spring Boot pour concevoir une application web permettant la gestion des produits, le suivi des stocks et la mise en place d'alertes sur seuil critique, avec gestion des rôles utilisateurs."], "achievements": []}, {"job_title": "Projet de Fin d'Études (PFE)", "company_name": "MEDICACOM", "duration": "Mai 2024", "location": null, "responsibilities": ["Développement d'une plateforme de gestion des ressources humaines.", "Conception et réalisation d'une application web RH complète en utilisant le MERN Stack (MongoDB, Express.js, React, Node.js).", "Cette solution permet une gestion centralisée des employés, des présences, des congés, des salaires et des processus RH au sein de l'entreprise."], "achievements": []}], "education": [{"degree_type": "Cycle Ingénieur en Génie Informatique", "institution_name": "IIT", "graduation_year": 2024, "gpa": null, "relevant_coursework": ["Étudiant en première année d'ingénierie en génie informatique"]}, {"degree_type": "Licence en Informatique de gestion", "institution_name": "Faculté des Sciences Économiques et de Gestion de Sfax", "graduation_year": 2024, "gpa": null, "relevant_coursework": []}], "skills": {"technical_skills": [], "soft_skills": [], "programming_languages": ["Python", "Java", "C", "C#", "JavaScript", "php"], "tools_and_technologies": ["ReactJS", "ExpressJS", "Postman", "Symfony", "Flutter", "MySQL", "MongoDB", "<PERSON><PERSON>", "NumPy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Spring Boot"]}, "certifications": [{"certification_name": "CrocoCoder: React", "issuing_organization": null, "date_obtained": null}, {"certification_name": "Zero to Mastery: <PERSON><PERSON><PERSON><PERSON>, <PERSON>js", "issuing_organization": null, "date_obtained": null}, {"certification_name": "CCNA 1 (Cisco Certified Network Associate 1): Introduction aux réseaux", "issuing_organization": null, "date_obtained": null}, {"certification_name": "Google Digital Marketing & E-commerce Professional Certificate", "issuing_organization": null, "date_obtained": null}], "projects": [{"project_name": "Développement d'un site éducatif - Expert", "description": "Création d'une plateforme web éducative permettant la gestion des utilisateurs inscrits et des cours proposés. Projet réalisé avec les technologies React, Express.js et MongoDB.", "technologies_used": ["React", "Express.js", "MongoDB"], "duration": null}, {"project_name": "Développement d'une application mobile", "description": "Création d'une application mobile d'e-commerce permettant de gérer les ventes. Projet réalisé avec la technologie Flutter.", "technologies_used": ["Flutter"], "duration": null}], "languages": [{"language": "<PERSON><PERSON>", "proficiency": "langue maternelle"}, {"language": "<PERSON><PERSON><PERSON>", "proficiency": "niveau professionnel"}, {"language": "Français", "proficiency": "niveau professionnel"}], "additional_information": {"vie_associative": [{"association": "Microsoft Tech Club - FSEGS", "details": [{"year": "2021-2022", "role": "Member"}, {"year": "2022-2023", "role": "responsable des médias et du marketing"}]}]}}