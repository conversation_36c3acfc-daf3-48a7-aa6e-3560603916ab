#!/usr/bin/env python3
"""
Complete CV Analyzer Script
Analyzes CV PDFs using Google Gemini Flash model and outputs structured JSON.

Usage:
    python cv_analyzer_complete.py <pdf_path>
    python cv_analyzer_complete.py pdf_cvs/file.pdf
    python cv_analyzer_complete.py /full/path/to/cv.pdf

Features:
- Loads API key from .env file
- Analyzes CV with Gemini Flash model
- Extracts structured information
- Saves results to JSON file
- All-in-one script with no external dependencies
"""

import os
import sys
import json
import re
import pathlib
from typing import Dict, Any, Optional
from datetime import datetime

# Import Google Gemini
try:
    from google import genai
    from google.genai import types
except ImportError:
    print("❌ Error: google-genai not installed")
    print("Install with: pip install google-genai")
    sys.exit(1)

def load_env_file(env_file: str = ".env") -> None:
    """
    Load environment variables from .env file.
    
    Args:
        env_file (str): Path to the .env file
    """
    try:
        with open(env_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    key = key.strip()
                    value = value.strip().strip('"').strip("'")
                    os.environ[key] = value
    except FileNotFoundError:
        print(f"Warning: {env_file} file not found")
    except Exception as e:
        print(f"Error loading {env_file}: {e}")

def get_gemini_api_key() -> Optional[str]:
    """
    Get Gemini API key from .env file or environment variable.
    
    Returns:
        Optional[str]: API key if found, None otherwise
    """
    # First, try to load from .env file
    load_env_file()
    
    # Try different possible key names
    return (os.getenv('api_key') or 
            os.getenv('GOOGLE_AI_API_KEY') or 
            os.getenv('GEMINI_API_KEY') or
            os.getenv('API_KEY'))

def extract_json_from_response(response_text: str) -> Dict[str, Any]:
    """
    Extract JSON data from Gemini response text.
    
    Args:
        response_text (str): Raw response from Gemini
    
    Returns:
        Dict[str, Any]: Parsed JSON data
    """
    try:
        # Try to find JSON content between ```json and ``` markers
        json_match = re.search(r'```json\s*(.*?)\s*```', response_text, re.DOTALL)
        if json_match:
            json_str = json_match.group(1)
        else:
            # Try to find JSON content between { and } markers
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                json_str = json_match.group(0)
            else:
                # If no JSON markers found, assume the entire response is JSON
                json_str = response_text
        
        # Parse the JSON
        cv_data = json.loads(json_str)
        return cv_data
        
    except json.JSONDecodeError as e:
        print(f"Error parsing JSON: {str(e)}")
        print(f"Response text: {response_text}")
        
        # Return a structured error response
        return {
            "error": "Failed to parse JSON response",
            "raw_response": response_text,
            "parse_error": str(e)
        }

def analyze_cv_with_gemini(pdf_path: str, api_key: str = None) -> Dict[str, Any]:
    """
    Analyze a CV PDF using Gemini Flash model and extract structured information.
    
    Args:
        pdf_path (str): Path to the PDF CV file
        api_key (str): Google AI API key (optional, will try to load from .env file)
    
    Returns:
        Dict[str, Any]: Structured CV information
    """
    
    # Initialize the Gemini client
    if api_key:
        client = genai.Client(api_key=api_key)
    else:
        # Try to get API key from .env file or environment
        env_api_key = get_gemini_api_key()
        if env_api_key:
            client = genai.Client(api_key=env_api_key)
        else:
            raise ValueError("No API key found. Please set api_key in .env file or pass it as parameter.")
    
    # Read the PDF file
    filepath = pathlib.Path(pdf_path)
    
    if not filepath.exists():
        raise FileNotFoundError(f"CV file not found: {pdf_path}")
    
    # Detailed prompt for CV analysis
    prompt = """
    Please analyze this CV/Resume document and extract the following information in a structured format:

    1. Personal Information:
       - Full name
       - Email address
       - Phone number
       - Address/Location
       - LinkedIn profile (if available)
       - Other social media/portfolio links

    2. Professional Summary/Bio:
       - Brief professional summary or objective

    3. Work Experience:
       - For each position: Job title, Company name, Duration (start-end dates), Location, Key responsibilities and achievements

    4. Education:
       - For each degree: Degree type, Institution name, Graduation year, GPA (if mentioned), Relevant coursework

    5. Skills:
       - Technical skills
       - Soft skills
       - Programming languages
       - Tools and technologies

    6. Certifications:
       - Certification name, Issuing organization, Date obtained

    7. Projects:
       - Project name, Description, Technologies used, Duration

    8. Languages:
       - Language and proficiency level

    9. Additional Information:
       - Awards, honors, publications, volunteer work, etc.

    Please format your response as a JSON object with clear structure and include all available information from the document. If any section is not present in the CV, include it with an empty array or null value.
    """
    
    try:
        # Generate content using Gemini Flash model
        response = client.models.generate_content(
            model="gemini-2.0-flash-exp",
            contents=[
                types.Part.from_bytes(
                    data=filepath.read_bytes(),
                    mime_type='application/pdf',
                ),
                prompt
            ]
        )
        
        # Extract the response text
        response_text = response.text
        
        # Try to extract JSON from the response
        cv_data = extract_json_from_response(response_text)
        
        return cv_data
        
    except Exception as e:
        print(f"Error analyzing CV: {str(e)}")
        return {"error": str(e)}

def save_cv_analysis(cv_data: Dict[str, Any], output_file: str) -> None:
    """
    Save CV analysis results to a JSON file.
    
    Args:
        cv_data (Dict[str, Any]): CV analysis data
        output_file (str): Output JSON file path
    """
    try:
        # Create output directory if it doesn't exist
        output_dir = os.path.dirname(output_file)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
            print(f"📁 Created directory: {output_dir}")
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(cv_data, f, indent=2, ensure_ascii=False)
        print(f"💾 CV analysis saved to {output_file}")
    except Exception as e:
        print(f"❌ Error saving CV analysis: {str(e)}")

def print_cv_summary(cv_data: Dict[str, Any]) -> None:
    """
    Print a nice summary of the CV analysis.
    
    Args:
        cv_data (Dict[str, Any]): CV analysis data
    """
    if "error" in cv_data:
        print(f"❌ Error: {cv_data['error']}")
        return
    
    print("\n📊 CV Analysis Summary:")
    print("=" * 50)
    
    # Personal Information
    if "personal_information" in cv_data:
        personal = cv_data["personal_information"]
        print(f"👤 Name: {personal.get('full_name', 'N/A')}")
        print(f"📧 Email: {personal.get('email_address', 'N/A')}")
        print(f"📱 Phone: {personal.get('phone_number', 'N/A')}")
        print(f"📍 Location: {personal.get('address', 'N/A')}")
        print()
    
    # Professional Summary
    if "professional_summary" in cv_data and cv_data["professional_summary"]:
        summary = cv_data["professional_summary"]
        if isinstance(summary, str) and len(summary) > 10:
            print(f"💼 Professional Summary:")
            print(f"   {summary[:200]}{'...' if len(summary) > 200 else ''}")
            print()
    
    # Work Experience
    if "work_experience" in cv_data and cv_data["work_experience"]:
        print(f"🏢 Work Experience ({len(cv_data['work_experience'])} positions):")
        for i, job in enumerate(cv_data["work_experience"][:3], 1):  # Show first 3
            print(f"   {i}. {job.get('job_title', 'N/A')} at {job.get('company_name', 'N/A')}")
            print(f"      Duration: {job.get('duration', 'N/A')}")
        if len(cv_data["work_experience"]) > 3:
            print(f"   ... and {len(cv_data['work_experience']) - 3} more")
        print()
    
    # Education
    if "education" in cv_data and cv_data["education"]:
        print(f"🎓 Education ({len(cv_data['education'])} entries):")
        for i, edu in enumerate(cv_data["education"], 1):
            print(f"   {i}. {edu.get('degree_type', 'N/A')} from {edu.get('institution_name', 'N/A')}")
            print(f"      Year: {edu.get('graduation_year', 'N/A')}")
        print()
    
    # Skills
    if "skills" in cv_data and cv_data["skills"]:
        skills = cv_data["skills"]
        if "programming_languages" in skills and skills["programming_languages"]:
            print(f"💻 Programming Languages: {', '.join(skills['programming_languages'])}")
        if "technical_skills" in skills and skills["technical_skills"]:
            tech_count = len(skills["technical_skills"])
            print(f"🔧 Technical Skills: {tech_count} skill(s)")
        print()

def main():
    """
    Main function to run CV analysis.
    """
    print("🔍 Complete CV Analyzer")
    print("=" * 50)
    
    # Check command line arguments
    if len(sys.argv) < 2:
        print("❌ Error: Please provide a PDF file path")
        print("Usage: python cv_analyzer_complete.py <pdf_path>")
        print("Examples:")
        print("  python cv_analyzer_complete.py pdf_cvs/file.pdf")
        print("  python cv_analyzer_complete.py /full/path/to/cv.pdf")
        print("  python cv_analyzer_complete.py C:\\Users\\<USER>\\cv.pdf")
        sys.exit(1)
    
    pdf_path = sys.argv[1]
    
    # Generate output filename
    if os.path.dirname(pdf_path):
        # If input has directory, create json_cvs in same parent directory
        parent_dir = os.path.dirname(os.path.dirname(pdf_path)) if os.path.dirname(pdf_path) else "."
        json_dir = os.path.join(parent_dir, "json_cvs")
    else:
        # If just filename, create json_cvs in current directory
        json_dir = "json_cvs"
    
    base_name = os.path.splitext(os.path.basename(pdf_path))[0]
    output_file = os.path.join(json_dir, f"{base_name}.json")
    
    print(f"📄 Input PDF: {pdf_path}")
    print(f"💾 Output JSON: {output_file}")
    
    # Check if PDF file exists
    if not os.path.exists(pdf_path):
        print(f"❌ Error: PDF file not found: {pdf_path}")
        sys.exit(1)
    
    # Check for API key
    print("🔑 Checking API key...")
    api_key = get_gemini_api_key()
    if not api_key:
        print("❌ Error: No API key found!")
        print("Please create a .env file with:")
        print('api_key="your-google-ai-api-key-here"')
        print("\nGet your API key from: https://aistudio.google.com/")
        sys.exit(1)
    
    print("✅ API key found")
    
    try:
        # Analyze the CV
        print("🤖 Analyzing CV with Gemini Flash model...")
        cv_data = analyze_cv_with_gemini(pdf_path)
        
        # Check for errors in response
        if "error" in cv_data:
            print(f"❌ Analysis failed: {cv_data['error']}")
            if "raw_response" in cv_data:
                print(f"Raw response: {cv_data['raw_response'][:200]}...")
            sys.exit(1)
        
        # Save results
        save_cv_analysis(cv_data, output_file)
        
        # Display summary
        print_cv_summary(cv_data)
        
        print(f"\n🎉 Analysis completed successfully!")
        print(f"📂 Full details saved in: {output_file}")
        
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
        print("Please check your API key and internet connection")
        sys.exit(1)

if __name__ == "__main__":
    main()
