import os
from typing import Optional

def load_env_file(env_file: str = ".env") -> None:
    """
    Load environment variables from .env file.

    Args:
        env_file (str): Path to the .env file
    """
    try:
        with open(env_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    key = key.strip()
                    value = value.strip().strip('"').strip("'")
                    os.environ[key] = value
    except FileNotFoundError:
        print(f"Warning: {env_file} file not found")
    except Exception as e:
        print(f"Error loading {env_file}: {e}")

def get_gemini_api_key() -> Optional[str]:
    """
    Get Gemini API key from .env file or environment variable.

    Returns:
        Optional[str]: API key if found, None otherwise
    """
    # First, try to load from .env file
    load_env_file()

    # Try different possible key names
    return (os.getenv('api_key') or
            os.getenv('GOOGLE_AI_API_KEY') or
            os.getenv('GEMINI_API_KEY') or
            os.getenv('API_KEY'))

def set_api_key_env(api_key: str) -> None:
    """
    Set the API key as an environment variable for the current session.
    
    Args:
        api_key (str): Your Google AI API key
    """
    os.environ['GOOGLE_AI_API_KEY'] = api_key
    print("API key set for current session")

# Example usage:
# To set your API key programmatically:
# set_api_key_env("your-api-key-here")

# Or set it as an environment variable before running:
# export GOOGLE_AI_API_KEY="your-api-key-here"  # Linux/Mac
# set GOOGLE_AI_API_KEY=your-api-key-here       # Windows
