import os
from typing import Optional

def get_gemini_api_key() -> Optional[str]:
    """
    Get Gemini API key from environment variable or return None.
    
    Returns:
        Optional[str]: API key if found, None otherwise
    """
    return os.getenv('GOOGLE_AI_API_KEY') or os.getenv('GEMINI_API_KEY')

def set_api_key_env(api_key: str) -> None:
    """
    Set the API key as an environment variable for the current session.
    
    Args:
        api_key (str): Your Google AI API key
    """
    os.environ['GOOGLE_AI_API_KEY'] = api_key
    print("API key set for current session")

# Example usage:
# To set your API key programmatically:
# set_api_key_env("your-api-key-here")

# Or set it as an environment variable before running:
# export GOOGLE_AI_API_KEY="your-api-key-here"  # Linux/Mac
# set GOOGLE_AI_API_KEY=your-api-key-here       # Windows
