from google import genai
from google.genai import types
import pathlib
import json
import re
from typing import Dict, Any

def analyze_cv_with_gemini(pdf_path: str, api_key: str = None) -> Dict[str, Any]:
    """
    Analyze a CV PDF using Gemini Flash model and extract structured information.
    
    Args:
        pdf_path (str): Path to the PDF CV file
        api_key (str): Google AI API key (optional if set in environment)
    
    Returns:
        Dict[str, Any]: Structured CV information
    """
    
    # Initialize the Gemini client
    if api_key:
        client = genai.Client(api_key=api_key)
    else:
        client = genai.Client()
    
    # Read the PDF file
    filepath = pathlib.Path(pdf_path)
    
    if not filepath.exists():
        raise FileNotFoundError(f"CV file not found: {pdf_path}")
    
    # Detailed prompt for CV analysis
    prompt = """
    Please analyze this CV/Resume document and extract the following information in a structured format:

    1. Personal Information:
       - Full name
       - Email address
       - Phone number
       - Address/Location
       - LinkedIn profile (if available)
       - Other social media/portfolio links

    2. Professional Summary/Bio:
       - Brief professional summary or objective

    3. Work Experience:
       - For each position: Job title, Company name, Duration (start-end dates), Location, Key responsibilities and achievements

    4. Education:
       - For each degree: Degree type, Institution name, Graduation year, GPA (if mentioned), Relevant coursework

    5. Skills:
       - Technical skills
       - Soft skills
       - Programming languages
       - Tools and technologies

    6. Certifications:
       - Certification name, Issuing organization, Date obtained

    7. Projects:
       - Project name, Description, Technologies used, Duration

    8. Languages:
       - Language and proficiency level

    9. Additional Information:
       - Awards, honors, publications, volunteer work, etc.

    Please format your response as a JSON object with clear structure and include all available information from the document. If any section is not present in the CV, include it with an empty array or null value.
    """
    
    try:
        # Generate content using Gemini Flash model
        response = client.models.generate_content(
            model="gemini-2.0-flash-exp",
            contents=[
                types.Part.from_bytes(
                    data=filepath.read_bytes(),
                    mime_type='application/pdf',
                ),
                prompt
            ]
        )
        
        # Extract the response text
        response_text = response.text
        
        # Try to extract JSON from the response
        cv_data = extract_json_from_response(response_text)
        
        return cv_data
        
    except Exception as e:
        print(f"Error analyzing CV: {str(e)}")
        return {"error": str(e)}

def extract_json_from_response(response_text: str) -> Dict[str, Any]:
    """
    Extract JSON data from Gemini response text.
    
    Args:
        response_text (str): Raw response from Gemini
    
    Returns:
        Dict[str, Any]: Parsed JSON data
    """
    try:
        # Try to find JSON content between ```json and ``` markers
        json_match = re.search(r'```json\s*(.*?)\s*```', response_text, re.DOTALL)
        if json_match:
            json_str = json_match.group(1)
        else:
            # Try to find JSON content between { and } markers
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                json_str = json_match.group(0)
            else:
                # If no JSON markers found, assume the entire response is JSON
                json_str = response_text
        
        # Parse the JSON
        cv_data = json.loads(json_str)
        return cv_data
        
    except json.JSONDecodeError as e:
        print(f"Error parsing JSON: {str(e)}")
        print(f"Response text: {response_text}")
        
        # Return a structured error response
        return {
            "error": "Failed to parse JSON response",
            "raw_response": response_text,
            "parse_error": str(e)
        }

def save_cv_analysis(cv_data: Dict[str, Any], output_file: str = "cv.json") -> None:
    """
    Save CV analysis results to a JSON file.
    
    Args:
        cv_data (Dict[str, Any]): CV analysis data
        output_file (str): Output JSON file path
    """
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(cv_data, f, indent=2, ensure_ascii=False)
        print(f"CV analysis saved to {output_file}")
    except Exception as e:
        print(f"Error saving CV analysis: {str(e)}")

def main():
    """
    Main function to analyze CV and save results.
    """
    # Configuration
    pdf_path = "pdf_cvs/file.pdf"
    output_file = "cv.json"
    
    print("Starting CV analysis...")
    print(f"Analyzing: {pdf_path}")
    
    # Analyze the CV
    cv_data = analyze_cv_with_gemini(pdf_path)
    
    # Save the results
    save_cv_analysis(cv_data, output_file)
    
    # Print summary
    if "error" not in cv_data:
        print("\nCV Analysis Summary:")
        if "personal_information" in cv_data and cv_data["personal_information"]:
            name = cv_data["personal_information"].get("full_name", "Unknown")
            print(f"Candidate: {name}")
        
        if "work_experience" in cv_data and cv_data["work_experience"]:
            print(f"Work Experience: {len(cv_data['work_experience'])} positions")
        
        if "education" in cv_data and cv_data["education"]:
            print(f"Education: {len(cv_data['education'])} entries")
        
        if "skills" in cv_data and cv_data["skills"]:
            print(f"Skills sections found")
    else:
        print(f"Error occurred: {cv_data['error']}")

if __name__ == "__main__":
    main()
