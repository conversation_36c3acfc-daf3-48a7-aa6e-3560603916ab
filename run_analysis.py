#!/usr/bin/env python3
"""
Simple script to analyze CV and save results to JSON.
Usage: python run_analysis.py <filename>
Example: python run_analysis.py file.pdf
"""

import os
import sys
from cv_analyzer import analyze_cv_with_gemini, save_cv_analysis
from config import get_gemini_api_key

def main(filename=None):
    """
    Main function to run CV analysis.

    Args:
        filename (str): Name of the PDF file in pdf_cvs/ folder
    """
    print("🔍 CV Analyzer - Starting Analysis")
    print("=" * 50)

    # Get filename from parameter or command line argument
    if filename is None:
        if len(sys.argv) < 2:
            print("❌ Error: Please provide a filename")
            print("Usage: python run_analysis.py <filename>")
            print("Example: python run_analysis.py file.pdf")
            return False
        filename = sys.argv[1]

    # Configuration
    pdf_path = f"pdf_cvs/{filename}"

    # Create json_cvs folder if it doesn't exist
    json_folder = "json_cvs"
    if not os.path.exists(json_folder):
        os.makedirs(json_folder)
        print(f"📁 Created folder: {json_folder}")

    # Generate output filename (same name but .json extension)
    base_name = os.path.splitext(filename)[0]
    output_file = f"{json_folder}/{base_name}.json"
    
    # Check if PDF file exists
    if not os.path.exists(pdf_path):
        print(f"❌ Error: CV file not found at {pdf_path}")
        print("Please make sure your CV file is placed in the pdf_cvs/ folder")
        return False
    
    # Check for API key
    print("🔑 Checking API key...")
    api_key = get_gemini_api_key()
    if not api_key:
        print("❌ Error: No API key found!")
        print("Please create a .env file with:")
        print('api_key="your-google-ai-api-key-here"')
        print("\nGet your API key from: https://aistudio.google.com/")
        return False
    
    print("✅ API key found")
    print(f"📄 Analyzing CV: {pdf_path}")
    print(f"💾 Output will be saved to: {output_file}")

    try:
        # Analyze the CV
        print("🤖 Sending CV to Gemini Flash model...")
        cv_data = analyze_cv_with_gemini(pdf_path)

        # Check for errors in response
        if "error" in cv_data:
            print(f"❌ Analysis failed: {cv_data['error']}")
            if "raw_response" in cv_data:
                print(f"Raw response: {cv_data['raw_response'][:200]}...")
            return False

        # Save results
        print("💾 Saving results...")
        save_cv_analysis(cv_data, output_file)

        # Display summary
        print("\n✅ Analysis Complete!")
        print(f"📁 Results saved to: {output_file}")
        print("\n📊 Analysis Summary:")
        print("-" * 30)
        
        # Personal Information
        if "personal_information" in cv_data and cv_data["personal_information"]:
            personal = cv_data["personal_information"]
            name = personal.get("full_name", "Not found")
            email = personal.get("email", "Not found")
            phone = personal.get("phone", "Not found")
            location = personal.get("address", "Not found")
            
            print(f"👤 Name: {name}")
            print(f"📧 Email: {email}")
            print(f"📱 Phone: {phone}")
            print(f"📍 Location: {location}")
        
        # Work Experience
        if "work_experience" in cv_data and cv_data["work_experience"]:
            exp_count = len(cv_data["work_experience"])
            print(f"💼 Work Experience: {exp_count} position(s)")
            
            # Show latest position
            if exp_count > 0:
                latest = cv_data["work_experience"][0]
                job_title = latest.get("job_title", "N/A")
                company = latest.get("company", "N/A")
                print(f"   Latest: {job_title} at {company}")
        
        # Education
        if "education" in cv_data and cv_data["education"]:
            edu_count = len(cv_data["education"])
            print(f"🎓 Education: {edu_count} entry(ies)")
            
            # Show highest education
            if edu_count > 0:
                highest = cv_data["education"][0]
                degree = highest.get("degree", "N/A")
                institution = highest.get("institution", "N/A")
                print(f"   Highest: {degree} from {institution}")
        
        # Skills
        if "skills" in cv_data and cv_data["skills"]:
            skills = cv_data["skills"]
            if "technical_skills" in skills and skills["technical_skills"]:
                tech_skills = skills["technical_skills"]
                if isinstance(tech_skills, list):
                    print(f"🔧 Technical Skills: {len(tech_skills)} skill(s)")
                    print(f"   Examples: {', '.join(tech_skills[:3])}")
            
            if "programming_languages" in skills and skills["programming_languages"]:
                prog_langs = skills["programming_languages"]
                if isinstance(prog_langs, list):
                    print(f"💻 Programming Languages: {', '.join(prog_langs[:5])}")
        
        # Professional Summary
        if "professional_summary" in cv_data and cv_data["professional_summary"]:
            summary = cv_data["professional_summary"]
            if isinstance(summary, str) and len(summary) > 10:
                print(f"📝 Professional Summary: Available ({len(summary)} characters)")
        
        print(f"\n🎉 Analysis completed successfully!")
        print(f"📂 Full details saved in: {output_file}")

        return True

    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
        print("Please check your API key and internet connection")
        return False

if __name__ == "__main__":
    print("CV Analyzer - Quick Run")
    print("Usage: python run_analysis.py <filename>")
    print("Example: python run_analysis.py file.pdf")
    print()
    print("Make sure you have:")
    print("1. Your CV file in pdf_cvs/ folder")
    print("2. API key in .env file: api_key=\"your-key-here\"")
    print("3. Installed dependencies: pip install -r requirements.txt")
    print()

    success = main()

    if success:
        print("\n✨ Done! Check json_cvs/ folder for the analysis results.")
    else:
        print("\n💡 Need help? Check the README.md file for setup instructions.")
        sys.exit(1)
