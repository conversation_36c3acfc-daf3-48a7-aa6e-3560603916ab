# CV Analyzer Complete - Usage Examples

## 🚀 All-in-One CV Analysis Script

The `cv_analyzer_complete.py` script combines everything into a single file:
- Loads API key from .env file
- Analyzes CV with Gemini Flash model
- Extracts structured information
- Saves results to JSON file
- No external dependencies (except google-genai)

## 📋 Usage Examples

### Basic Usage
```bash
python cv_analyzer_complete.py <pdf_path>
```

### Example Commands

#### 1. Relative Path (Recommended)
```bash
python cv_analyzer_complete.py pdf_cvs/file.pdf
python cv_analyzer_complete.py pdf_cvs/file2.pdf
python cv_analyzer_complete.py pdf_cvs/john_doe_cv.pdf
```

#### 2. Full Absolute Path
```bash
# Windows
python cv_analyzer_complete.py C:\Users\<USER>\cv.pdf
python cv_analyzer_complete.py "C:\Users\<USER>\Desktop\my_cv.pdf"

# Linux/Mac
python cv_analyzer_complete.py /home/<USER>/documents/cv.pdf
python cv_analyzer_complete.py "/Users/<USER>/Desktop/my cv.pdf"
```

#### 3. Current Directory
```bash
python cv_analyzer_complete.py my_cv.pdf
```

## 📁 Output Structure

The script automatically creates output in `json_cvs/` folder:

```
Input: pdf_cvs/file.pdf          → Output: json_cvs/file.json
Input: pdf_cvs/john_cv.pdf       → Output: json_cvs/john_cv.json
Input: /full/path/resume.pdf     → Output: json_cvs/resume.json
Input: my_cv.pdf                 → Output: json_cvs/my_cv.json
```

## 🔧 Setup Requirements

### 1. Install Dependencies
```bash
pip install google-genai
```

### 2. Create .env File
```
api_key="your-google-ai-api-key-here"
```

### 3. Get API Key
- Go to [Google AI Studio](https://aistudio.google.com/)
- Create an API key for Gemini
- Add it to your .env file

## 📊 Sample Output

### Terminal Output
```
🔍 Complete CV Analyzer
==================================================
📄 Input PDF: pdf_cvs/file.pdf
💾 Output JSON: json_cvs\file.json
🔑 Checking API key...
✅ API key found
🤖 Analyzing CV with Gemini Flash model...
💾 CV analysis saved to json_cvs\file.json

📊 CV Analysis Summary:
==================================================
👤 Name: Ahmad Ben Abdallah
📧 Email: <EMAIL>
📱 Phone: +216 26567756
📍 Location: Sfax, Tunisia

💼 Professional Summary:
   Étudiant passionné en informatique, actuellement en 1ère année...

🏢 Work Experience (1 positions):
   1. Stage d'été at DSP
      Duration: Juillet - Août 2023

🎓 Education (2 entries):
   1. Cycle Ingénieur en Génie Informatique from IIT
      Year: 2024

💻 Programming Languages: Python, Java, C, C#, JavaScript, php
🔧 Technical Skills: 5 skill(s)

🎉 Analysis completed successfully!
📂 Full details saved in: json_cvs\file.json
```

### JSON Output Structure
```json
{
  "personal_information": {
    "full_name": "Ahmad Ben Abdallah",
    "email_address": "<EMAIL>",
    "phone_number": "+216 26567756",
    "address": "Sfax, Tunisia",
    "linkedin_profile": "www.linkedin.com/in/ahmad-benabdallah-",
    "other_social_media_portfolio_links": [...]
  },
  "professional_summary": "...",
  "work_experience": [...],
  "education": [...],
  "skills": {...},
  "certifications": [...],
  "projects": [...],
  "languages": [...],
  "additional_information": {...}
}
```

## ❌ Error Handling

### No PDF Path Provided
```bash
python cv_analyzer_complete.py
# Output: ❌ Error: Please provide a PDF file path
```

### File Not Found
```bash
python cv_analyzer_complete.py nonexistent.pdf
# Output: ❌ Error: PDF file not found: nonexistent.pdf
```

### No API Key
```bash
# If .env file missing or empty
# Output: ❌ Error: No API key found!
#         Please create a .env file with: api_key="your-key-here"
```

### API Errors
```bash
# If API is overloaded or other issues
# Output: ❌ Analysis failed: 503 UNAVAILABLE. The model is overloaded.
```

## 🎯 Key Features

1. **Single File Solution** - Everything in one script
2. **Flexible Input** - Accepts any PDF path format
3. **Auto Directory Creation** - Creates json_cvs folder automatically
4. **Comprehensive Error Handling** - Clear error messages
5. **Rich Terminal Output** - Beautiful formatted summary
6. **Structured JSON Output** - Complete CV analysis data
7. **Cross-Platform** - Works on Windows, Linux, Mac

## 🔄 Workflow

1. **Place CV PDF** anywhere accessible
2. **Run script** with PDF path
3. **Get JSON output** in json_cvs folder
4. **View summary** in terminal
5. **Use JSON data** for further processing

## 💡 Tips

- Use quotes around paths with spaces: `"my cv file.pdf"`
- The script preserves the original filename in output
- JSON files are UTF-8 encoded for international characters
- All directories are created automatically if they don't exist
- The script works with any PDF CV format
