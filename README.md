# CV Analyzer with Gemini Flash

This project uses Google's Gemini Flash model to analyze CV/Resume PDFs and extract structured information such as candidate name, bio, contact information, work experience, education, skills, and more.

## Features

- 📄 **PDF CV Analysis**: Reads and analyzes PDF resume files
- 🤖 **AI-Powered Extraction**: Uses Gemini Flash model for intelligent content extraction
- 📊 **Structured Output**: Extracts information into organized JSON format
- 💾 **JSON Export**: Saves analysis results to `cv.json` file
- 🔧 **Configurable**: Easy API key management and customization

## Installation

1. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Get Google AI API Key**:
   - Go to [Google AI Studio](https://aistudio.google.com/)
   - Create an API key for Gemini
   - Set it as an environment variable:
     ```bash
     # Linux/Mac
     export GOOGLE_AI_API_KEY="your-api-key-here"
     
     # Windows
     set GOOGLE_AI_API_KEY=your-api-key-here
     ```

## Usage

### Basic Usage

1. **Place your CV PDF** in the `pdf_cvs/` folder (or update the path in the script)

2. **Run the analyzer**:
   ```bash
   python cv_analyzer.py
   ```

3. **Check the results** in `cv.json`

### Advanced Usage

```python
from cv_analyzer import analyze_cv_with_gemini, save_cv_analysis

# Analyze CV with custom API key
cv_data = analyze_cv_with_gemini("path/to/cv.pdf", api_key="your-key")

# Save to custom file
save_cv_analysis(cv_data, "custom_output.json")
```

### Example Usage Script

Run the example script to see different usage patterns:
```bash
python example_usage.py
```

## Output Format

The analyzer extracts the following information into JSON format:

```json
{
  "personal_information": {
    "full_name": "John Doe",
    "email": "<EMAIL>",
    "phone": "******-567-8900",
    "address": "City, State, Country",
    "linkedin": "linkedin.com/in/johndoe",
    "portfolio": "johndoe.com"
  },
  "professional_summary": "Brief professional summary...",
  "work_experience": [
    {
      "job_title": "Software Engineer",
      "company": "Tech Company",
      "duration": "2020-2023",
      "location": "City, State",
      "responsibilities": ["Developed applications...", "Led team of..."]
    }
  ],
  "education": [
    {
      "degree": "Bachelor of Computer Science",
      "institution": "University Name",
      "graduation_year": "2020",
      "gpa": "3.8/4.0"
    }
  ],
  "skills": {
    "technical_skills": ["Python", "JavaScript", "SQL"],
    "programming_languages": ["Python", "Java", "C++"],
    "tools_technologies": ["Docker", "AWS", "Git"]
  },
  "certifications": [
    {
      "name": "AWS Certified Developer",
      "issuer": "Amazon Web Services",
      "date": "2022"
    }
  ],
  "projects": [
    {
      "name": "Project Name",
      "description": "Project description...",
      "technologies": ["React", "Node.js"],
      "duration": "3 months"
    }
  ],
  "languages": [
    {
      "language": "English",
      "proficiency": "Native"
    }
  ],
  "additional_information": {
    "awards": [],
    "publications": [],
    "volunteer_work": []
  }
}
```

## Files Description

- **`cv_analyzer.py`**: Main script for CV analysis
- **`config.py`**: Configuration utilities for API key management
- **`example_usage.py`**: Example usage patterns and demonstrations
- **`requirements.txt`**: Python dependencies
- **`pdf_cvs/file.pdf`**: Sample CV file location
- **`cv.json`**: Output file with analysis results

## Error Handling

The script includes comprehensive error handling for:
- Missing PDF files
- API key issues
- JSON parsing errors
- Network connectivity problems

If an error occurs, it will be included in the output JSON with details for debugging.

## Customization

You can customize the analysis by modifying the prompt in the `analyze_cv_with_gemini()` function to extract additional information or change the output format.

## Requirements

- Python 3.7+
- Google AI API key
- PDF CV file
- Internet connection for API calls

## Troubleshooting

1. **API Key Issues**: Make sure your Google AI API key is valid and properly set
2. **PDF Reading Issues**: Ensure the PDF is not password-protected or corrupted
3. **JSON Parsing Issues**: Check the raw response in the error output for debugging

## License

This project is for educational and personal use. Please respect the terms of service for Google AI APIs.
