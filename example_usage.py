#!/usr/bin/env python3
"""
Example usage of the CV analyzer script.
"""

from cv_analyzer import analyze_cv_with_gemini, save_cv_analysis
from config import set_api_key_env, get_gemini_api_key
import json

def example_with_api_key():
    """
    Example: Analyze CV with API key provided directly.
    """
    # Replace with your actual API key
    api_key = "your-google-ai-api-key-here"
    
    # Analyze the CV
    cv_data = analyze_cv_with_gemini("pdf_cvs/file.pdf", api_key=api_key)
    
    # Save to JSON
    save_cv_analysis(cv_data, "cv_analysis_example.json")
    
    return cv_data

def example_with_env_variable():
    """
    Example: Analyze CV using environment variable for API key.
    """
    # Set API key as environment variable
    set_api_key_env("your-google-ai-api-key-here")
    
    # Analyze the CV (API key will be read from environment)
    cv_data = analyze_cv_with_gemini("pdf_cvs/file.pdf")
    
    # Save to JSON
    save_cv_analysis(cv_data, "cv_analysis_env.json")
    
    return cv_data

def print_cv_summary(cv_data):
    """
    Print a nice summary of the CV analysis.
    """
    if "error" in cv_data:
        print(f"❌ Error: {cv_data['error']}")
        return
    
    print("📄 CV Analysis Summary")
    print("=" * 50)
    
    # Personal Information
    if "personal_information" in cv_data:
        personal = cv_data["personal_information"]
        print(f"👤 Name: {personal.get('full_name', 'N/A')}")
        print(f"📧 Email: {personal.get('email', 'N/A')}")
        print(f"📱 Phone: {personal.get('phone', 'N/A')}")
        print(f"📍 Location: {personal.get('address', 'N/A')}")
        print()
    
    # Professional Summary
    if "professional_summary" in cv_data and cv_data["professional_summary"]:
        print(f"💼 Professional Summary:")
        print(f"   {cv_data['professional_summary']}")
        print()
    
    # Work Experience
    if "work_experience" in cv_data and cv_data["work_experience"]:
        print(f"🏢 Work Experience ({len(cv_data['work_experience'])} positions):")
        for i, job in enumerate(cv_data["work_experience"][:3], 1):  # Show first 3
            print(f"   {i}. {job.get('job_title', 'N/A')} at {job.get('company', 'N/A')}")
            print(f"      Duration: {job.get('duration', 'N/A')}")
        if len(cv_data["work_experience"]) > 3:
            print(f"   ... and {len(cv_data['work_experience']) - 3} more")
        print()
    
    # Education
    if "education" in cv_data and cv_data["education"]:
        print(f"🎓 Education ({len(cv_data['education'])} entries):")
        for i, edu in enumerate(cv_data["education"], 1):
            print(f"   {i}. {edu.get('degree', 'N/A')} from {edu.get('institution', 'N/A')}")
            print(f"      Year: {edu.get('graduation_year', 'N/A')}")
        print()
    
    # Skills
    if "skills" in cv_data and cv_data["skills"]:
        skills = cv_data["skills"]
        if "technical_skills" in skills and skills["technical_skills"]:
            print(f"🔧 Technical Skills: {', '.join(skills['technical_skills'][:5])}...")
        if "programming_languages" in skills and skills["programming_languages"]:
            print(f"💻 Programming: {', '.join(skills['programming_languages'])}")
        print()

if __name__ == "__main__":
    print("CV Analyzer Example Usage")
    print("=" * 30)
    
    # Check if API key is available
    api_key = get_gemini_api_key()
    if not api_key:
        print("⚠️  No API key found in environment variables.")
        print("Please set GOOGLE_AI_API_KEY or GEMINI_API_KEY environment variable")
        print("or modify the example functions to include your API key.")
        print()
        print("Example:")
        print("export GOOGLE_AI_API_KEY='your-api-key-here'  # Linux/Mac")
        print("set GOOGLE_AI_API_KEY=your-api-key-here       # Windows")
    else:
        print("✅ API key found in environment")
        
        # Run the analysis
        try:
            cv_data = analyze_cv_with_gemini("pdf_cvs/file.pdf")
            save_cv_analysis(cv_data, "cv.json")
            print_cv_summary(cv_data)
        except Exception as e:
            print(f"❌ Error running analysis: {e}")
