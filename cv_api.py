#!/usr/bin/env python3
"""
CV Analysis API Server
Provides two endpoints:
1. POST /analyze - Submit CV file for analysis
2. GET /status/<job_id> - Check analysis status and get results
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
import os
import json
import uuid
import threading
import time
from datetime import datetime
from cv_analyzer import analyze_cv_with_gemini, save_cv_analysis

app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# In-memory job tracking (in production, use Redis or database)
jobs = {}

class JobStatus:
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"

def analyze_cv_async(job_id, pdf_path, output_file):
    """
    Asynchronous CV analysis function.
    Updates job status as it progresses.
    """
    try:
        # Update status to running
        jobs[job_id]["status"] = JobStatus.RUNNING
        jobs[job_id]["message"] = "Analyzing CV with Gemini Flash model..."
        jobs[job_id]["updated_at"] = datetime.now().isoformat()
        
        # Perform the analysis
        cv_data = analyze_cv_with_gemini(pdf_path)
        
        # Check for errors in analysis
        if "error" in cv_data:
            jobs[job_id]["status"] = JobStatus.FAILED
            jobs[job_id]["message"] = f"Analysis failed: {cv_data['error']}"
            jobs[job_id]["error"] = cv_data["error"]
            jobs[job_id]["updated_at"] = datetime.now().isoformat()
            return
        
        # Save results
        save_cv_analysis(cv_data, output_file)
        
        # Update status to completed
        jobs[job_id]["status"] = JobStatus.COMPLETED
        jobs[job_id]["message"] = "Analysis completed successfully"
        jobs[job_id]["result"] = cv_data
        jobs[job_id]["output_file"] = output_file
        jobs[job_id]["updated_at"] = datetime.now().isoformat()
        
    except Exception as e:
        # Handle any unexpected errors
        jobs[job_id]["status"] = JobStatus.FAILED
        jobs[job_id]["message"] = f"Unexpected error: {str(e)}"
        jobs[job_id]["error"] = str(e)
        jobs[job_id]["updated_at"] = datetime.now().isoformat()

@app.route('/analyze', methods=['POST'])
def analyze_cv():
    """
    API endpoint to submit CV file for analysis.
    
    Expected JSON payload:
    {
        "filename": "file.pdf"
    }
    
    Returns:
    {
        "job_id": "uuid-string",
        "status": "pending",
        "message": "CV analysis job created"
    }
    """
    try:
        # Get filename from request
        data = request.get_json()
        if not data or 'filename' not in data:
            return jsonify({
                "error": "Missing 'filename' in request body"
            }), 400
        
        filename = data['filename']
        pdf_path = f"pdf_cvs/{filename}"
        
        # Check if file exists
        if not os.path.exists(pdf_path):
            return jsonify({
                "error": f"CV file not found: {pdf_path}"
            }), 404
        
        # Generate unique job ID
        job_id = str(uuid.uuid4())
        
        # Create output filename based on input
        base_name = os.path.splitext(filename)[0]
        output_file = f"{base_name}_analysis.json"
        
        # Initialize job tracking
        jobs[job_id] = {
            "job_id": job_id,
            "filename": filename,
            "pdf_path": pdf_path,
            "output_file": output_file,
            "status": JobStatus.PENDING,
            "message": "CV analysis job created",
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
            "result": None,
            "error": None
        }
        
        # Start analysis in background thread
        thread = threading.Thread(
            target=analyze_cv_async,
            args=(job_id, pdf_path, output_file)
        )
        thread.daemon = True
        thread.start()
        
        return jsonify({
            "job_id": job_id,
            "status": JobStatus.PENDING,
            "message": "CV analysis job created successfully"
        }), 202
        
    except Exception as e:
        return jsonify({
            "error": f"Failed to create analysis job: {str(e)}"
        }), 500

@app.route('/status/<job_id>', methods=['GET'])
def get_job_status(job_id):
    """
    API endpoint to check job status and get results.
    
    Returns different responses based on job status:
    
    PENDING/RUNNING:
    {
        "job_id": "uuid-string",
        "status": "running",
        "message": "running...."
    }
    
    COMPLETED:
    {
        "job_id": "uuid-string",
        "status": "completed",
        "message": "Analysis completed successfully",
        "result": { ... CV analysis data ... }
    }
    
    FAILED:
    {
        "job_id": "uuid-string",
        "status": "failed",
        "message": "Error message",
        "error": "Detailed error"
    }
    """
    try:
        # Check if job exists
        if job_id not in jobs:
            return jsonify({
                "error": "Job not found"
            }), 404
        
        job = jobs[job_id]
        
        # Prepare response based on status
        response = {
            "job_id": job_id,
            "status": job["status"],
            "message": job["message"],
            "created_at": job["created_at"],
            "updated_at": job["updated_at"]
        }
        
        # Add result if completed
        if job["status"] == JobStatus.COMPLETED:
            response["result"] = job["result"]
            response["output_file"] = job["output_file"]
        
        # Add error details if failed
        elif job["status"] == JobStatus.FAILED:
            response["error"] = job["error"]
        
        # For running status, show "running...." message
        elif job["status"] == JobStatus.RUNNING:
            response["message"] = "running...."
        
        return jsonify(response), 200
        
    except Exception as e:
        return jsonify({
            "error": f"Failed to get job status: {str(e)}"
        }), 500

@app.route('/jobs', methods=['GET'])
def list_jobs():
    """
    API endpoint to list all jobs (for debugging/monitoring).
    """
    try:
        job_list = []
        for job_id, job in jobs.items():
            job_summary = {
                "job_id": job_id,
                "filename": job["filename"],
                "status": job["status"],
                "message": job["message"],
                "created_at": job["created_at"],
                "updated_at": job["updated_at"]
            }
            job_list.append(job_summary)
        
        return jsonify({
            "jobs": job_list,
            "total": len(job_list)
        }), 200
        
    except Exception as e:
        return jsonify({
            "error": f"Failed to list jobs: {str(e)}"
        }), 500

@app.route('/health', methods=['GET'])
def health_check():
    """
    Health check endpoint.
    """
    return jsonify({
        "status": "healthy",
        "message": "CV Analysis API is running",
        "timestamp": datetime.now().isoformat()
    }), 200

@app.errorhandler(404)
def not_found(error):
    return jsonify({
        "error": "Endpoint not found"
    }), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({
        "error": "Internal server error"
    }), 500

if __name__ == '__main__':
    print("🚀 Starting CV Analysis API Server...")
    print("📋 Available endpoints:")
    print("  POST /analyze - Submit CV for analysis")
    print("  GET /status/<job_id> - Check analysis status")
    print("  GET /jobs - List all jobs")
    print("  GET /health - Health check")
    print()
    print("🔧 Make sure you have:")
    print("  1. CV files in pdf_cvs/ folder")
    print("  2. API key in .env file")
    print("  3. Dependencies installed: pip install flask flask-cors")
    print()
    
    # Run the Flask app
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        threaded=True
    )
