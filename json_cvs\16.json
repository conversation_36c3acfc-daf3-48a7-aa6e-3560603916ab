{"fullname": "ACHRAF AYDI", "email": "<EMAIL>", "phone": "+216.23.310.042", "title": "Ingénieur en Électronique", "summary": "Après 8 ans dans le domaine d’éléctronique, je souhaite me réorienter vers le domaine d’informatique. Fort de mes compétences en systèmes Informatiques et Réseaux qui obtenu a partir de mon parcours universitaires et mon stage dans le domaine et ma grande passion pour les technologies de l'information, l'innovation et le développement de logiciels, m'a poussé à choisir ce domaine, Maintenant je suis prêt(e) à relever de nouveaux défis dans ce domaine.", "location": "Sfax-Tunisie", "website": "https://www.linkedin.com/in/achraf-aydi", "skills": [{"name": "C", "level": "Intermédiaire"}, {"name": "C++", "level": "Intermédiaire"}, {"name": "Python", "level": "Intermédiaire"}, {"name": "ASP.NET", "level": "Intermédiaire"}, {"name": "HTML", "level": "Intermédiaire"}, {"name": "CSS", "level": "Intermédiaire"}, {"name": "JAVA", "level": "Intermédiaire"}, {"name": "XML", "level": "Intermédiaire"}, {"name": "MySQL", "level": "Intermédiaire"}, {"name": "PostgreSQL", "level": "Intermédiaire"}, {"name": "Oracle", "level": "Intermédiaire"}, {"name": "TCP/IP", "level": "Intermédiaire"}, {"name": "routage", "level": "Intermédiaire"}, {"name": "commutation", "level": "Intermédiaire"}, {"name": "s<PERSON><PERSON><PERSON><PERSON>", "level": "Intermédiaire"}, {"name": "Git", "level": "Intermédiaire"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "level": "Intermédiaire"}, {"name": "Android Studio", "level": "Intermédiaire"}, {"name": "Visual Studio", "level": "Intermédiaire"}, {"name": "Raspberry Pi", "level": "Intermédiaire"}, {"name": "ESP32", "level": "Intermédiaire"}, {"name": "PIC 16F877A", "level": "Intermédiaire"}, {"name": "<PERSON><PERSON><PERSON>", "level": "Intermédiaire"}, {"name": "MikroC", "level": "Intermédiaire"}, {"name": "Matlab", "level": "Intermédiaire"}, {"name": "SQL", "level": "Intermédiaire"}, {"name": "AutoCAD", "level": "Intermédiaire"}, {"name": "Cisco Packet Tracer", "level": "Intermédiaire"}, {"name": "DevOps", "level": "Intermédiaire"}, {"name": "Big Data", "level": "Intermédiaire"}, {"name": "Data Science", "level": "Intermédiaire"}, {"name": "Intelligence Artificielle", "level": "Intermédiaire"}, {"name": "Contrôle de la Température d’une maison intelligente", "level": "Intermédiaire"}, {"name": "Conception, réalisation et implémentation d'une carte électronique", "level": "Intermédiaire"}, {"name": "Programation d'un PIC 16F877A", "level": "Intermédiaire"}], "experiences": [{"title": "Ingénieur en Électronique", "company": "SMARTEC", "location": "SFAX", "start_date": "2020-01-01", "end_date": null, "is_current": true, "description": "Responsable Du Service Après-Vente\nGérer et suivre les problèmes techniques avec les fournisseurs\nResponsable des ventes et de la production de Films de Protection pour Smartphones (SMT)\nConcevez les nouveaux films de protection avec SolidWorks."}, {"title": "Technicien Sup<PERSON>ur", "company": "AMI MOBILE TUNISIE", "location": "SFAX", "start_date": "2017-09-01", "end_date": "2020-01-01", "is_current": false, "description": "Maintenance matérielle et logicielle des appareils téléphoniques.\nRéparation de cartes électroniques.\nCréation d'application web"}, {"title": "Stage PFE (Mastère)", "company": "COMPI TECHNOLOGY", "location": "SFAX", "start_date": "2020-05-01", "end_date": "2022-12-01", "is_current": false, "description": "Développer une application Android permettant le contrôle à distance (Bluetooth et Wifi) d'un système domotique avec android Studio (Java / XML).\nRéalisation d'un système de contrôle domotique basé sur une carte Arduino ESP 32."}, {"title": "Stage de Technicien", "company": "ASSISTANCE TECHNIQUE TUNISIENNE \" TAT”", "location": null, "start_date": "2016-06-01", "end_date": "2016-06-30", "is_current": false, "description": null}, {"title": "Stage PFE (Licence)", "company": "ENET'COM", "location": "SFAX", "start_date": "2017-03-01", "end_date": "2017-07-01", "is_current": false, "description": null}], "educations": [{"degree": "Mastère Professionnel spécialité “Développement des Systèmes Informatiques et Réseaux (DSIR)”", "institution": "Institut Supérieur des Etudes Technologiques de Sfax (ISET)", "location": null, "start_date": "2024-09-01", "end_date": "2026-07-01", "description": null}, {"degree": "Mastère Professionnel en Réseaux Informatiques et Télécommunications (RITEL)", "institution": "École Nationale d'Électronique et de Télécommunications (ENET'COM)", "location": null, "start_date": "2019-02-01", "end_date": "2021-12-01", "description": null}, {"degree": "Licence fondamentale en sciences et technologies de l'information et de la communication (STIC): Spécialité électronique", "institution": "École Nationale d'Électronique et de Télécommunications (ENET'COM)", "location": null, "start_date": "2014-09-01", "end_date": "2017-06-01", "description": null}, {"degree": "Baccalauréat technique", "institution": "Lycée He<PERSON>", "location": null, "start_date": "2014", "end_date": null, "description": null}], "languages": [{"name": "<PERSON><PERSON>", "level": "<PERSON><PERSON>"}, {"name": "Français", "level": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "level": "Intermédiaire"}, {"name": "Espagnol", "level": "Débutant"}]}