#!/usr/bin/env python3
"""
Test client for CV Analysis API
Demonstrates how to use both endpoints.
"""

import requests
import time
import json

# API base URL
BASE_URL = "http://localhost:5000"

def test_cv_analysis(filename):
    """
    Test the complete CV analysis workflow.
    
    Args:
        filename (str): Name of the PDF file in pdf_cvs/ folder
    """
    print(f"🔍 Testing CV Analysis API with file: {filename}")
    print("=" * 60)
    
    # Step 1: Submit CV for analysis
    print("📤 Step 1: Submitting CV for analysis...")
    
    analyze_url = f"{BASE_URL}/analyze"
    payload = {"filename": filename}
    
    try:
        response = requests.post(analyze_url, json=payload)
        
        if response.status_code == 202:
            result = response.json()
            job_id = result["job_id"]
            print(f"✅ Job created successfully!")
            print(f"   Job ID: {job_id}")
            print(f"   Status: {result['status']}")
            print(f"   Message: {result['message']}")
        else:
            print(f"❌ Failed to create job: {response.status_code}")
            print(f"   Error: {response.json()}")
            return
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return
    
    # Step 2: Poll for status until completion
    print(f"\n🔄 Step 2: Polling for status...")
    status_url = f"{BASE_URL}/status/{job_id}"
    
    max_attempts = 60  # Maximum 5 minutes (60 * 5 seconds)
    attempt = 0
    
    while attempt < max_attempts:
        try:
            response = requests.get(status_url)
            
            if response.status_code == 200:
                result = response.json()
                status = result["status"]
                message = result["message"]
                
                print(f"   Attempt {attempt + 1}: Status = {status}, Message = {message}")
                
                if status == "completed":
                    print(f"\n✅ Analysis completed successfully!")
                    print(f"   Job ID: {job_id}")
                    print(f"   Output file: {result.get('output_file', 'N/A')}")
                    
                    # Display summary of results
                    if "result" in result:
                        cv_data = result["result"]
                        print(f"\n📊 Analysis Summary:")
                        print(f"   👤 Name: {cv_data.get('personal_information', {}).get('full_name', 'N/A')}")
                        print(f"   📧 Email: {cv_data.get('personal_information', {}).get('email_address', 'N/A')}")
                        print(f"   📱 Phone: {cv_data.get('personal_information', {}).get('phone_number', 'N/A')}")
                        print(f"   📍 Location: {cv_data.get('personal_information', {}).get('address', 'N/A')}")
                        
                        if "work_experience" in cv_data:
                            exp_count = len(cv_data["work_experience"])
                            print(f"   💼 Work Experience: {exp_count} position(s)")
                        
                        if "education" in cv_data:
                            edu_count = len(cv_data["education"])
                            print(f"   🎓 Education: {edu_count} entry(ies)")
                    
                    return result
                
                elif status == "failed":
                    print(f"\n❌ Analysis failed!")
                    print(f"   Error: {result.get('error', 'Unknown error')}")
                    return result
                
                elif status in ["pending", "running"]:
                    # Continue polling
                    time.sleep(5)  # Wait 5 seconds before next check
                    attempt += 1
                    continue
                
            else:
                print(f"❌ Status check failed: {response.status_code}")
                print(f"   Error: {response.json()}")
                return
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Status request failed: {e}")
            time.sleep(5)
            attempt += 1
            continue
    
    print(f"\n⏰ Timeout: Analysis took too long (>{max_attempts * 5} seconds)")

def test_health_check():
    """
    Test the health check endpoint.
    """
    print("🏥 Testing health check...")
    
    try:
        response = requests.get(f"{BASE_URL}/health")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ API is healthy!")
            print(f"   Status: {result['status']}")
            print(f"   Message: {result['message']}")
            print(f"   Timestamp: {result['timestamp']}")
        else:
            print(f"❌ Health check failed: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Health check request failed: {e}")

def test_list_jobs():
    """
    Test the list jobs endpoint.
    """
    print("📋 Testing list jobs...")
    
    try:
        response = requests.get(f"{BASE_URL}/jobs")
        
        if response.status_code == 200:
            result = response.json()
            jobs = result["jobs"]
            total = result["total"]
            
            print(f"✅ Found {total} job(s):")
            for job in jobs:
                print(f"   - Job ID: {job['job_id']}")
                print(f"     File: {job['filename']}")
                print(f"     Status: {job['status']}")
                print(f"     Created: {job['created_at']}")
                print()
        else:
            print(f"❌ List jobs failed: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ List jobs request failed: {e}")

def main():
    """
    Main function to run API tests.
    """
    print("🧪 CV Analysis API Test Client")
    print("=" * 40)
    
    # Test health check first
    test_health_check()
    print()
    
    # Test CV analysis with file2.pdf (based on your recent change)
    test_cv_analysis("file2.pdf")
    print()
    
    # Test list jobs
    test_list_jobs()
    print()
    
    print("🎉 API testing completed!")

if __name__ == "__main__":
    print("Make sure the API server is running:")
    print("python cv_api.py")
    print()
    
    # Ask user for confirmation
    input("Press Enter to start testing (or Ctrl+C to cancel)...")
    
    main()
