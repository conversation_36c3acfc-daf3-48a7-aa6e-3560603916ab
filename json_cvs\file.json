{"personal_information": {"full_name": "<PERSON>", "email_address": "contactbenab<PERSON><PERSON><PERSON><PERSON>@gmail.com", "phone_number": "+216 26567756", "address": "Sfax, Tunisia", "linkedin_profile": "www.linkedin.com/in/ahmad-ben<PERSON><PERSON><PERSON>-", "other_social_media_portfolio_links": ["https://github.com/ahamdbenabdallah", "https://ahamdbenabdalla.github.io/portfolio-website-3d/"]}, "professional_summary": "Étudiant passionné en informatique, actuellement en 1ère année d'ingénierie en génie informatique à l'IIT. Fortement intéressé par le développement web et mobile, je suis curieux, autonome et motivé par l'apprentissage continu.", "work_experience": [{"job_title": "Stage d'été", "company_name": "DSP", "duration": "Juillet - Août 2023", "location": null, "responsibilities": ["Développement d'une plateforme de gestion de stock dans le cadre d'un projet de développement web dynamique, au sein de la startup DSP.", "Utilisation des technologies Spring Boot pour concevoir une application web permettant la gestion des produits, le suivi des stocks et la mise en place d'alertes sur seuil critique, avec gestion des rôles utilisateurs."]}], "education": [{"degree_type": "Cycle Ingénieur en Génie Informatique", "institution_name": "IIT", "graduation_year": 2024, "gpa": null, "relevant_coursework": "Étudiant en première année d'ingénierie en génie informatique"}, {"degree_type": "Licence en Informatique de gestion", "institution_name": "Faculté des Sciences Économiques et de Gestion de Sfax", "graduation_year": 2024, "gpa": null, "relevant_coursework": null}], "skills": {"technical_skills": ["Apprentissage automatique: apprentissage supervisé et non supervisé.", "Conception: langage de modélisation unifié (UML).", "Systèmes d'exploitation : Unix, Windows.", "Gestion de projet : Scrum.", "Analy<PERSON>, visualisation et traitement des données : <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Seaborn."], "soft_skills": [], "programming_languages": ["Python", "Java", "C", "C#", "JavaScript", "php"], "tools_and_technologies": ["ReactJS", "ExpressJS", "Postman", "Symfony", "Flutter", "MySQL", "MongoDB"]}, "certifications": [{"certification_name": "CrocoCoder", "issuing_organization": "React", "date_obtained": null}, {"certification_name": "Zero to Mastery", "issuing_organization": "Nodejs, Expressjs", "date_obtained": null}, {"certification_name": "CCNA 1 (Cisco Certified Network Associate 1)", "issuing_organization": "Introduction aux réseaux", "date_obtained": null}, {"certification_name": "Google Digital Marketing & E-commerce Professional Certificate", "issuing_organization": null, "date_obtained": null}], "projects": [{"project_name": "Projet de Fin d'Études (PFE) - MEDICACOM", "description": "Développement d'une plateforme de gestion des ressources humaines. Conception et réalisation d'une application web RH complète en utilisant le MERN Stack (MongoDB, Express.js, React, Node.js). Cette solution permet une gestion centralisée des employés, des présences, des congés, des salaires et des processus RH au sein de l'entreprise.", "technologies_used": ["MongoDB", "Express.js", "React", "Node.js"], "duration": "Mai 2024"}, {"project_name": "Développement d'un site éducatif – Expert", "description": "Création d'une plateforme web éducative permettant la gestion des utilisateurs inscrits et des cours proposés. Projet réalisé avec les technologies React, Express.js et MongoDB.", "technologies_used": ["React", "Express.js", "MongoDB"], "duration": null}, {"project_name": "Développement d'une application mobile", "description": "Création d'une application mobile d’e-commerce permettant de gérer les ventes. Projet réalisé avec la technologie Flutter.", "technologies_used": ["Flutter"], "duration": null}], "languages": [{"language": "<PERSON><PERSON>", "proficiency_level": "langue maternelle"}, {"language": "<PERSON><PERSON><PERSON>", "proficiency_level": "niveau professionnel"}, {"language": "Français", "proficiency_level": "niveau professionnel"}], "additional_information": {"associations": [{"name": "Microsoft Tech Club - FSEGS", "years": ["2021-2022: Member", "2022-2023: responsable des médias et du marketing"]}]}}