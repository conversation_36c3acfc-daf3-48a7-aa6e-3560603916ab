{"fullname": "AHMAD BEN ABDALLAH", "title": "Étudiant en Génie Informatique", "summary": "Étudiant passionné en informatique, actuellement en 1ère année\nd’ingénierie en génie informatique à l’IIT.\nFortement intéressé par le développement web et mobile, je\nsuis curieux, autonome et motivé par l’apprentissage continu.", "location": "Sfax, Tunisia", "website": "https://www.linkedin.com/in/ahmad-<PERSON><PERSON><PERSON><PERSON>-", "skills": [{"name": "Python", "level": "Intermédiaire"}, {"name": "Java", "level": "Intermédiaire"}, {"name": "C", "level": "Intermédiaire"}, {"name": "C#", "level": "Intermédiaire"}, {"name": "JavaScript", "level": "Intermédiaire"}, {"name": "php", "level": "Intermédiaire"}, {"name": "ReactJS", "level": "Intermédiaire"}, {"name": "ExpressJS", "level": "Intermédiaire"}, {"name": "Postman", "level": "Intermédiaire"}, {"name": "Symfony", "level": "Intermédiaire"}, {"name": "Flutter", "level": "Intermédiaire"}, {"name": "MySQL", "level": "Intermédiaire"}, {"name": "MongoDB", "level": "Intermédiaire"}, {"name": "apprentissage supervisé", "level": "Intermédiaire"}, {"name": "apprentissage non supervisé", "level": "Intermédiaire"}, {"name": "<PERSON><PERSON>", "level": "Intermédiaire"}, {"name": "NumPy", "level": "Intermédiaire"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "level": "Intermédiaire"}, {"name": "<PERSON><PERSON>", "level": "Intermédiaire"}, {"name": "UML", "level": "Intermédiaire"}, {"name": "Unix", "level": "Intermédiaire"}, {"name": "Windows", "level": "Intermédiaire"}, {"name": "Scrum", "level": "Intermédiaire"}], "experiences": [{"title": "Projet de Fin d’Études (PFE)", "company": "MEDICACOM", "location": null, "start_date": "2024-05-01", "end_date": null, "is_current": true, "description": "Développement d’une plateforme de gestion des ressources\nhumaines .\nConception et réalisation d’une application web RH complète en\nutilisant le MERN Stack (MongoDB, Express.js, React, Node.js).\nCette solution permet une gestion centralisée des employés, des\nprésences, des congés, des salaires et des processus RH au sein\nde l’entreprise."}, {"title": "Stage d’été", "company": "DSP", "location": null, "start_date": "2023-07-01", "end_date": "2023-08-01", "is_current": false, "description": "Développement d’une plateforme de gestion de stock dans le\ncadre d’un projet de développement web dynamique, au sein de\nla startup DSP.\nUtilisation des technologies Spring Boot pour concevoir une\napplication web permettant la gestion des produits, le suivi des\nstocks et la mise en place d’alertes sur seuil critique, avec gestion\ndes rôles utilisateurs."}, {"title": "Projet d’un site éducatif", "company": "Expert", "location": null, "start_date": null, "end_date": null, "is_current": false, "description": "Création d’une plateforme web éducative permettant la gestion\ndes utilisateurs inscrits et des cours proposés.\nProjet réalisé avec les technologies React, Express.js et MongoDB."}, {"title": "Application mobile", "company": null, "location": null, "start_date": null, "end_date": null, "is_current": false, "description": "Création d’une application mobile d’e-commerce permettant de\ngérer les ventes.\nProjet réalisé avec la technologie Flutter."}], "educations": [{"degree": "Cycle Ingénieur en Génie Informatique", "institution": "IIT", "location": null, "start_date": "2024-01-01", "end_date": null, "description": "Étudiant en première année d’ingénierie en génie informatique"}, {"degree": "Licence en Informatique de gestion", "institution": "Faculté des Sciences Économiques et de Gestion de Sfax", "location": null, "start_date": "2024-01-01", "end_date": null, "description": null}], "languages": [{"name": "<PERSON><PERSON>", "level": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "level": "<PERSON><PERSON><PERSON>"}, {"name": "Français", "level": "<PERSON><PERSON><PERSON>"}]}